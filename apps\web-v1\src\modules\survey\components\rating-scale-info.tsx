'use client'

import { HexagonIcon } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { motion } from 'framer-motion'
import React from 'react'
import {
  RATING_CONFIG,
  RATING_SCALE,
} from '../constants/survey.constants'

interface RatingScaleInfoProps {
  maxValue?: number
  minValue?: number
  className?: string
}

export default function RatingScaleInfo({
  maxValue = RATING_SCALE.max,
  minValue = RATING_SCALE.min,
  className = '',
}: RatingScaleInfoProps) {
  return (
    <motion.div
      className={`space-y-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
    >
      <Typography variant="body1" className="mb-3 text-gray-800">
        <PERSON><PERSON><PERSON> câu hỏi về sự hài lòng của khách hàng đư<PERSON><PERSON> đánh gi<PERSON> theo thang
        điểm {maxValue} với ý nghĩa như sau:
      </Typography>
      <ul className="space-y-2">
        <li className="flex items-baseline space-x-2">
          <HexagonIcon size={12} className="shrink-0" />
          <Typography variant="body1" className="text-gray-800">
            <span className="font-semibold">Hài lòng:</span>{' '}
            {RATING_CONFIG.thresholds.satisfied} - {maxValue} điểm
          </Typography>
        </li>
        <li className="flex items-center space-x-2">
          <HexagonIcon size={12} className="shrink-0" />
          <Typography variant="body1" className="text-gray-800">
            <span className="font-semibold">Bình thường:</span>{' '}
            {RATING_CONFIG.thresholds.neutral} -{' '}
            {RATING_CONFIG.thresholds.satisfied - 1} điểm
          </Typography>
        </li>
        <li className="flex items-center space-x-2">
          <HexagonIcon size={12} className="shrink-0" />
          <Typography variant="body1" className="text-gray-800">
            <span className="font-semibold">Không hài lòng:</span>{' '}
            {minValue} - {RATING_CONFIG.thresholds.neutral - 1} điểm
          </Typography>
        </li>
      </ul>
    </motion.div>
  )
}
