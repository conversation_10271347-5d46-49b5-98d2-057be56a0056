'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { ArrowLeft } from 'lucide-react'
import { useSurveyContext } from '../context/survey-context'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'
import BaseStep from './base-step'
import { QuestionsList } from './question-renderer'
import RatingScaleInfo from './rating-scale-info'

interface SurveyQuestionsStepProps {
  stepConfig?: any
}

export default function SurveyQuestionsStep({
  stepConfig,
}: SurveyQuestionsStepProps) {
  const { getCurrentStep } = useSurveyContext()
  const { handleButtonAction, canGoNext, canGoPrevious } = useSurveyNavigation()

  const step = stepConfig || getCurrentStep()

  // Check if step has rating questions and should show labels
  const hasRatingQuestions = step?.questions?.some(
    (question: any) => question.type === 'rating' && question.showLabels !== false
  )

  // Get max and min values from rating questions
  const ratingQuestion = step?.questions?.find((q: any) => q.type === 'rating') as any
  const maxValue = ratingQuestion?.maxValue || 10
  const minValue = ratingQuestion?.minValue || 1

  if (!step || !step.questions) {
    return (
      <BaseStep
        step={
          step || {
            id: 'questions',
            title: 'Câu hỏi khảo sát',
            type: 'questions',
            order: 1,
          }
        }
      >
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted-foreground">
            Không có câu hỏi nào được cấu hình cho bước này.
          </Typography>
        </div>
      </BaseStep>
    )
  }

  const renderStepButtons = (
    <>
      {canGoPrevious && (
        <MainButton
          label="QUAY LẠI"
          variant="primary"
          iconPosition="left"
          CustomIcon={ArrowLeft}
          onClick={() => handleButtonAction('previous')}
        />
      )}

      <MainButton
        variant="secondary"
        label={canGoNext ? 'TIẾP TỤC' : 'GỬI'}
        onClick={() => handleButtonAction(canGoNext ? 'next' : 'submit')}
      />
    </>
  )

  return (
    <BaseStep step={step}>
      {/* Rating Scale Info - Show before questions if there are rating questions */}
      {hasRatingQuestions && (
        <RatingScaleInfo
          maxValue={maxValue}
          minValue={minValue}
          className="mb-8"
        />
      )}

      {/* Questions List */}
      <QuestionsList questions={step.questions} className="space-y-10" />

      {/* Render Button */}
      {renderStepButtons && (
        <div className="flex justify-center gap-x-4 xl:gap-x-10">{renderStepButtons}</div>
      )}
    </BaseStep>
  )
}
