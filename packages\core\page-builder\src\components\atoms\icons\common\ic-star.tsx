import { cn } from '@ttplatform/ui/lib'
import React from 'react'

interface StarIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
  className?: string
}

export const StarIcon: React.FC<StarIconProps> = ({ size = 24, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={cn(
        '[filter:drop-shadow(0px_16px_28.8px_rgba(0,0,0,0.03))_drop-shadow(0px_5.11px_9.19px_rgba(0,0,0,0.19))_drop-shadow(0px_1.93px_3.48px_rgba(0,0,0,0.24))',
        className
      )}
    >
      <path
        fill="currentColor"
        d="m5.825 21l1.625-7.025L2 9.25l7.2-.625L12 2l2.8 6.625l7.2.625l-5.45 4.725L18.175 21L12 17.275z"
      />
    </svg>
  )
}
