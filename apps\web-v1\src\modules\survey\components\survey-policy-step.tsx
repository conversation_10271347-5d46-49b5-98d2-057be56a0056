'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { useSurveyContext } from '../context/survey-context'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'
import BaseStep from './base-step'

interface SurveyPolicyStepProps {
  stepConfig?: any // Will be passed from dynamic config
}

export default function SurveyPolicyStep({
  stepConfig,
}: SurveyPolicyStepProps) {
  const { handleButtonAction } = useSurveyNavigation()
  const { getCurrentStep } = useSurveyContext()

  // Use dynamic config if available, otherwise use current step from context
  const step = stepConfig || getCurrentStep()

  if (!step) {
    return null
  }

  // Default content if not provided in config
  const defaultContent = {
    infoBox: {
      title:
        'Cảm ơn Quý khách hàng đã tin tưởng lựa chọn Dịch vụ hỗ trợ sản phẩm của <PERSON>ú Thái Cat.',
      description: `<div>
            <div><PERSON>ớ<PERSON> mục đích liên tục cải thiện chất lượng dịch vụ để phục vụ Quý khách hàng ngày càng tốt hơn, Phú Thái Cat kính mời quý khách dành ra <strong>3 phút</strong> để đánh giá và phản hồi về mức độ hài lòng của quý khách khi giao dịch với chúng tôi</div>
            <div>Phú Thái Cat sẽ <strong>liên hệ để gửi tặng một phần quà</strong> sau khi Quý khách hàng hoàn thành khảo sát này.</div>
            </div>`,
    },
    policy: {
      title: 'CHÍNH SÁCH KHẢO SÁT',
      content: [
        'Khảo sát này hoàn toàn tuân theo Bộ Luật Dân sự Việt Nam 2015.',
        'Những thông tin do khách hàng cung cấp sẽ được chia sẻ với Phú Thái Cat, Caterpillar và công ty nghiên cứu thị trường GCOMM. Phú Thái Cat cam kết bảo mật tuyệt đối mọi thông tin của Quý khách hàng và chỉ sử dụng vào mục đích nghiên cứu.',
        'Sau khảo sát này, có thể nhân viên phụ trách của Phú Thái Cat sẽ liên hệ với Quý khách để giải quyết những khúc mắc (nếu có).',
        'Quý khách hàng có đồng ý tham gia Khảo sát về mức độ hài lòng trong giao dịch với Phú Thái Cat không?',
      ],
    },
  }

  // Parse content from step config or use default
  const content = step.content ? JSON.parse(step.content) : defaultContent

  const renderStepButtons = (
    <>
      <MainButton
        label="KHÔNG ĐỒNG Ý"
        isDisabledIcon
        className="!px-[22px]"
        onClick={() => handleButtonAction('home', '/')}
      />
      <MainButton
        variant="secondary"
        label="ĐỒNG Ý"
        isDisabledIcon
        className="!px-[22px]"
        onClick={() => handleButtonAction('next')}
      />
    </>
  )

  return (
    <BaseStep step={step}>
      {/* Info box */}
      <div>
        <Typography variant="body1" className="font-bold">
          {content?.infoBox?.title}
        </Typography>
        <div
          className="!leading-8 text-sm md:text-lg lg:text-lg"
          dangerouslySetInnerHTML={{ __html: content?.infoBox?.description }}
        />
      </div>

      {/* Line */}
      <div className="h-px w-full bg-gray-200 mx-auto"></div>

      {/* Main content */}
      <div className="space-y-4">
        <Typography variant="h4">{content.policy.title}</Typography>
        <div className="space-y-4">
          {content.policy.content.map((paragraph: string, index: number) => (
            <Typography key={index} variant="body1">
              {paragraph}
            </Typography>
          ))}
        </div>
      </div>

      {/* Render Button */}
      {renderStepButtons && (
        <div className="flex justify-center gap-x-4 xl:gap-x-10">{renderStepButtons}</div>
      )}
    </BaseStep>
  )
}
